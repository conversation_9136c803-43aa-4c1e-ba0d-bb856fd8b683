{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useTheme } from 'next-themes';\nimport { motion } from 'framer-motion';\nimport { Sun, Moon } from 'lucide-react';\n\nexport function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const { theme, setTheme } = useTheme();\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return (\n      <div className=\"w-10 h-10 rounded-full glass flex items-center justify-center\">\n        <div className=\"w-5 h-5 bg-slate-400 rounded-full animate-pulse\" />\n      </div>\n    );\n  }\n\n  return (\n    <motion.button\n      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n      className=\"relative w-10 h-10 rounded-full glass hover:glass-strong transition-all duration-300 flex items-center justify-center group\"\n      whileHover={{ scale: 1.1 }}\n      whileTap={{ scale: 0.9 }}\n      aria-label=\"Toggle theme\"\n    >\n      <motion.div\n        initial={false}\n        animate={{\n          scale: theme === 'dark' ? 1 : 0,\n          rotate: theme === 'dark' ? 0 : 180,\n        }}\n        transition={{ duration: 0.3 }}\n        className=\"absolute\"\n      >\n        <Moon className=\"w-5 h-5 text-teal-400\" />\n      </motion.div>\n      \n      <motion.div\n        initial={false}\n        animate={{\n          scale: theme === 'light' ? 1 : 0,\n          rotate: theme === 'light' ? 0 : -180,\n        }}\n        transition={{ duration: 0.3 }}\n        className=\"absolute\"\n      >\n        <Sun className=\"w-5 h-5 text-yellow-400\" />\n      </motion.div>\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;QACrD,WAAU;QACV,YAAY;YAAE,OAAO;QAAI;QACzB,UAAU;YAAE,OAAO;QAAI;QACvB,cAAW;;0BAEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,OAAO,UAAU,SAAS,IAAI;oBAC9B,QAAQ,UAAU,SAAS,IAAI;gBACjC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAGlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,OAAO,UAAU,UAAU,IAAI;oBAC/B,QAAQ,UAAU,UAAU,IAAI,CAAC;gBACnC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB;GAjDgB;;QAEc,mJAAA,CAAA,WAAQ;;;KAFtB", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X, Home, User, Briefcase, Code, Youtube, Mail } from 'lucide-react';\nimport { ThemeToggle } from '@/components/ui/ThemeToggle';\n\nconst navItems = [\n  { name: 'Home', href: '#home', icon: Home },\n  { name: 'About', href: '#about', icon: User },\n  { name: 'Skills', href: '#skills', icon: Code },\n  { name: 'Projects', href: '#projects', icon: Briefcase },\n  { name: 'YouTube', href: '#youtube', icon: Youtube },\n  { name: 'Contact', href: '#contact', icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n      \n      // Update active section based on scroll position\n      const sections = navItems.map(item => item.href.slice(1));\n      const currentSection = sections.find(section => {\n        const element = document.getElementById(section);\n        if (element) {\n          const rect = element.getBoundingClientRect();\n          return rect.top <= 100 && rect.bottom >= 100;\n        }\n        return false;\n      });\n      \n      if (currentSection) {\n        setActiveSection(currentSection);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    const element = document.getElementById(href.slice(1));\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsOpen(false);\n  };\n\n  return (\n    <>\n      {/* Desktop Navigation */}\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          scrolled ? 'glass-strong' : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"font-display font-bold text-xl gradient-text\"\n            >\n              Portfolio\n            </motion.div>\n\n            {/* Desktop Menu */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <motion.button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                    activeSection === item.href.slice(1)\n                      ? 'text-teal-400'\n                      : 'text-slate-300 hover:text-white'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {item.name}\n                  {activeSection === item.href.slice(1) && (\n                    <motion.div\n                      layoutId=\"activeSection\"\n                      className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-teal-400\"\n                      initial={false}\n                      transition={{ type: 'spring', stiffness: 380, damping: 30 }}\n                    />\n                  )}\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Theme Toggle & Mobile Menu Button */}\n            <div className=\"flex items-center space-x-4\">\n              <ThemeToggle />\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"md:hidden p-2 rounded-lg glass hover:glass-strong transition-all duration-200\"\n                aria-label=\"Toggle menu\"\n              >\n                {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.nav>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"fixed inset-0 z-40 md:hidden\"\n          >\n            <div className=\"fixed inset-0 bg-navy/80 backdrop-blur-sm\" onClick={() => setIsOpen(false)} />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed right-0 top-0 h-full w-64 glass-strong border-l border-slate-700\"\n            >\n              <div className=\"flex flex-col p-6 pt-20\">\n                {navItems.map((item, index) => (\n                  <motion.button\n                    key={item.name}\n                    onClick={() => scrollToSection(item.href)}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors duration-200 ${\n                      activeSection === item.href.slice(1)\n                        ? 'bg-teal-500/20 text-teal-400'\n                        : 'text-slate-300 hover:text-white hover:bg-slate-800/50'\n                    }`}\n                  >\n                    <item.icon className=\"w-5 h-5\" />\n                    <span>{item.name}</span>\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC1C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC9C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,2MAAA,CAAA,UAAO;IAAC;IACnD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;oBAE7B,iDAAiD;oBACjD,MAAM,WAAW,SAAS,GAAG;sEAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,KAAK,CAAC;;oBACtD,MAAM,iBAAiB,SAAS,IAAI;4EAAC,CAAA;4BACnC,MAAM,UAAU,SAAS,cAAc,CAAC;4BACxC,IAAI,SAAS;gCACX,MAAM,OAAO,QAAQ,qBAAqB;gCAC1C,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI;4BAC3C;4BACA,OAAO;wBACT;;oBAEA,IAAI,gBAAgB;wBAClB,iBAAiB;oBACnB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,KAAK,CAAC;QACnD,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,UAAU;IACZ;IAEA,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,WAAW,CAAC,4DAA4D,EACtE,WAAW,iBAAiB,kBAC5B;0BAEF,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;0CACX;;;;;;0CAKD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAW,CAAC,sEAAsE,EAChF,kBAAkB,KAAK,IAAI,CAAC,KAAK,CAAC,KAC9B,kBACA,mCACJ;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;4CAEvB,KAAK,IAAI;4CACT,kBAAkB,KAAK,IAAI,CAAC,KAAK,CAAC,oBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,UAAS;gDACT,WAAU;gDACV,SAAS;gDACT,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;;;;;;;uCAhBzD,KAAK,IAAI;;;;;;;;;;0CAwBpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0IAAA,CAAA,cAAW;;;;;kDACZ,6LAAC;wCACC,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;wCACV,cAAW;kDAEV,uBAAS,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;4BAA4C,SAAS,IAAM,UAAU;;;;;;sCACpF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,WAAW,CAAC,0FAA0F,EACpG,kBAAkB,KAAK,IAAI,CAAC,KAAK,CAAC,KAC9B,iCACA,yDACJ;;0DAEF,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,KAAK,IAAI;;;;;;;uCAZX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBlC;GA7IgB;KAAA", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YAC<PERSON>,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'ghost' | 'neon';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-teal-500 hover:bg-teal-600 text-white focus:ring-teal-500 neon-glow',\n      secondary: 'glass border-teal-500/30 text-teal-400 hover:border-teal-400 hover:text-teal-300',\n      ghost: 'text-slate-300 hover:text-white hover:bg-slate-800/50',\n      neon: 'bg-gradient-to-r from-teal-500 to-cyan-400 text-navy font-semibold hover:from-teal-400 hover:to-cyan-300 neon-glow',\n    };\n\n    const sizes = {\n      sm: 'px-4 py-2 text-sm',\n      md: 'px-6 py-3 text-base',\n      lg: 'px-8 py-4 text-lg',\n    };\n\n    return (\n      <motion.button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        transition={{ type: 'spring', stiffness: 400, damping: 17 }}\n        {...props}\n      >\n        {children}\n      </motion.button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpE,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;QACzD,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Github, Linkedin, Twitter } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\n\nconst greetings = ['Hi, I\\'m', 'Hello, I\\'m', 'Hey there, I\\'m'];\n\nexport function Hero() {\n  const [currentGreeting, setCurrentGreeting] = useState(0);\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentGreeting((prev) => (prev + 1) % greetings.length);\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const scrollToProjects = () => {\n    const element = document.getElementById('projects');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-navy via-slate-900 to-navy\" />\n      <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(20,184,166,0.1),transparent_50%)]\" />\n\n      {/* Animated Background Particles */}\n      <div className=\"absolute inset-0\">\n        {[...Array(50)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-teal-400/30 rounded-full\"\n            initial={{\n              x: Math.random() * 1200,\n              y: Math.random() * 800,\n            }}\n            animate={{\n              x: Math.random() * 1200,\n              y: Math.random() * 800,\n            }}\n            transition={{\n              duration: Math.random() * 20 + 10,\n              repeat: Infinity,\n              repeatType: 'reverse',\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Greeting Animation */}\n          <motion.div\n            key={currentGreeting}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.5 }}\n            className=\"text-xl md:text-2xl text-slate-300 font-medium\"\n          >\n            {greetings[currentGreeting]}\n          </motion.div>\n\n          {/* Name */}\n          <motion.h1\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-5xl md:text-7xl lg:text-8xl font-display font-bold gradient-text leading-tight\"\n          >\n            Alex Johnson\n          </motion.h1>\n\n          {/* Title */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"space-y-4\"\n          >\n            <h2 className=\"text-2xl md:text-4xl font-semibold text-white\">\n              Creative Frontend Developer\n            </h2>\n            <h3 className=\"text-xl md:text-2xl text-teal-400\">\n              & UI/UX Designer\n            </h3>\n          </motion.div>\n\n          {/* Description */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"max-w-2xl mx-auto text-lg md:text-xl text-slate-300 leading-relaxed\"\n          >\n            Crafting exceptional digital experiences with modern technologies,\n            innovative design, and a passion for creating user-centered solutions\n            that make a difference.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Button variant=\"neon\" size=\"lg\" onClick={scrollToProjects}>\n              View My Work\n            </Button>\n            <Button variant=\"secondary\" size=\"lg\">\n              Download Resume\n            </Button>\n          </motion.div>\n\n          {/* Social Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1 }}\n            className=\"flex justify-center space-x-6\"\n          >\n            {[\n              { icon: Github, href: 'https://github.com', label: 'GitHub' },\n              { icon: Linkedin, href: 'https://linkedin.com', label: 'LinkedIn' },\n              { icon: Twitter, href: 'https://twitter.com', label: 'Twitter' },\n            ].map(({ icon: Icon, href, label }) => (\n              <motion.a\n                key={label}\n                href={href}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"p-3 rounded-full glass hover:glass-strong transition-all duration-300 group\"\n                whileHover={{ scale: 1.1, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n                aria-label={label}\n              >\n                <Icon className=\"w-6 h-6 text-slate-400 group-hover:text-teal-400 transition-colors duration-300\" />\n              </motion.a>\n            ))}\n          </motion.div>\n        </motion.div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 1, delay: 1.5 }}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        >\n          <motion.div\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"flex flex-col items-center space-y-2 text-slate-400\"\n          >\n            <span className=\"text-sm\">Scroll to explore</span>\n            <ChevronDown className=\"w-5 h-5\" />\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,YAAY;IAAC;IAAY;IAAe;CAAkB;AAEzD,SAAS;;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,WAAW;2CAAY;oBAC3B;mDAAmB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;;gBAC5D;0CAAG;YAEH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG,KAAK,MAAM,KAAK;4BACnB,GAAG,KAAK,MAAM,KAAK;wBACrB;wBACA,SAAS;4BACP,GAAG,KAAK,MAAM,KAAK;4BACnB,GAAG,KAAK,MAAM,KAAK;wBACrB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,KAAK;4BAC/B,QAAQ;4BACR,YAAY;wBACd;uBAdK;;;;;;;;;;0BAmBX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAET,SAAS,CAAC,gBAAgB;+BAPtB;;;;;0CAWP,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CACX;;;;;;0CAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAG9D,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;;0CAMpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CACX;;;;;;0CAOD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAO,MAAK;wCAAK,SAAS;kDAAkB;;;;;;kDAG5D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,MAAK;kDAAK;;;;;;;;;;;;0CAMxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAE;gCACtC,WAAU;0CAET;oCACC;wCAAE,MAAM,yMAAA,CAAA,SAAM;wCAAE,MAAM;wCAAsB,OAAO;oCAAS;oCAC5D;wCAAE,MAAM,6MAAA,CAAA,WAAQ;wCAAE,MAAM;wCAAwB,OAAO;oCAAW;oCAClE;wCAAE,MAAM,2MAAA,CAAA,UAAO;wCAAE,MAAM;wCAAuB,OAAO;oCAAU;iCAChE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM;wCACN,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAK,GAAG,CAAC;wCAAE;wCAChC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,cAAY;kDAEZ,cAAA,6LAAC;4CAAK,WAAU;;;;;;uCATX;;;;;;;;;;;;;;;;kCAgBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;wBACtC,WAAU;kCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC;GArKgB;KAAA", "debugId": null}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'framer-motion';\nimport { useRef } from 'react';\n\nconst skillCategories = [\n  {\n    title: 'Frontend Development',\n    skills: [\n      { name: 'React', level: 95, icon: '⚛️' },\n      { name: 'Next.js', level: 90, icon: '▲' },\n      { name: 'TypeScript', level: 88, icon: '📘' },\n      { name: 'Tailwind CSS', level: 92, icon: '🎨' },\n      { name: 'Framer Motion', level: 85, icon: '🎭' },\n    ],\n  },\n  {\n    title: 'Backend & Tools',\n    skills: [\n      { name: 'Node.js', level: 80, icon: '🟢' },\n      { name: 'Python', level: 75, icon: '🐍' },\n      { name: 'PostgreSQL', level: 78, icon: '🐘' },\n      { name: 'Git', level: 90, icon: '📚' },\n      { name: 'Docker', level: 70, icon: '🐳' },\n    ],\n  },\n  {\n    title: 'Design & UX',\n    skills: [\n      { name: 'Figma', level: 88, icon: '🎨' },\n      { name: 'Adobe XD', level: 82, icon: '🎭' },\n      { name: 'Photoshop', level: 85, icon: '🖼️' },\n      { name: 'UI/UX Design', level: 90, icon: '✨' },\n      { name: 'Prototyping', level: 87, icon: '🔧' },\n    ],\n  },\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n    },\n  },\n};\n\nexport function Skills() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: '-100px' });\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-slate-900/50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\"\n          >\n            Skills & Expertise\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-slate-300 max-w-3xl mx-auto\"\n          >\n            A comprehensive toolkit of modern technologies and design principles \n            to bring your ideas to life\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\"\n        >\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              variants={itemVariants}\n              className=\"glass rounded-2xl p-8 hover:glass-strong transition-all duration-300\"\n            >\n              <h3 className=\"text-2xl font-semibold text-white mb-8 text-center\">\n                {category.title}\n              </h3>\n              \n              <div className=\"space-y-6\">\n                {category.skills.map((skill, skillIndex) => (\n                  <motion.div\n                    key={skill.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}\n                    transition={{\n                      duration: 0.6,\n                      delay: categoryIndex * 0.2 + skillIndex * 0.1,\n                    }}\n                    className=\"space-y-2\"\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-2xl\">{skill.icon}</span>\n                        <span className=\"text-slate-200 font-medium\">{skill.name}</span>\n                      </div>\n                      <span className=\"text-teal-400 font-semibold\">{skill.level}%</span>\n                    </div>\n                    \n                    <div className=\"relative h-2 bg-slate-700 rounded-full overflow-hidden\">\n                      <motion.div\n                        initial={{ width: 0 }}\n                        animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}\n                        transition={{\n                          duration: 1,\n                          delay: categoryIndex * 0.2 + skillIndex * 0.1 + 0.3,\n                          ease: 'easeOut',\n                        }}\n                        className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-teal-500 to-cyan-400 rounded-full\"\n                      />\n                      <motion.div\n                        initial={{ opacity: 0 }}\n                        animate={isInView ? { opacity: [0, 1, 0] } : { opacity: 0 }}\n                        transition={{\n                          duration: 0.5,\n                          delay: categoryIndex * 0.2 + skillIndex * 0.1 + 1,\n                        }}\n                        className=\"absolute top-0 left-0 h-full w-full bg-gradient-to-r from-transparent via-white/30 to-transparent\"\n                        style={{ transform: `translateX(${skill.level}%)` }}\n                      />\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Additional Skills Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 40 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"mt-16\"\n        >\n          <h3 className=\"text-2xl font-semibold text-center text-white mb-8\">\n            Technologies I Work With\n          </h3>\n          \n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              'JavaScript', 'React', 'Next.js', 'TypeScript', 'Node.js', 'Python',\n              'Tailwind CSS', 'Framer Motion', 'PostgreSQL', 'MongoDB', 'Firebase',\n              'AWS', 'Vercel', 'Figma', 'Adobe Creative Suite', 'Git', 'Docker'\n            ].map((tech, index) => (\n              <motion.span\n                key={tech}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}\n                transition={{ duration: 0.4, delay: 1 + index * 0.05 }}\n                whileHover={{ scale: 1.1, y: -2 }}\n                className=\"px-4 py-2 glass rounded-full text-sm font-medium text-slate-300 hover:text-teal-400 hover:glass-strong transition-all duration-300 cursor-default\"\n              >\n                {tech}\n              </motion.span>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAS,OAAO;gBAAI,MAAM;YAAK;YACvC;gBAAE,MAAM;gBAAW,OAAO;gBAAI,MAAM;YAAI;YACxC;gBAAE,MAAM;gBAAc,OAAO;gBAAI,MAAM;YAAK;YAC5C;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,MAAM;YAAK;YAC9C;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,MAAM;YAAK;SAChD;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,MAAM;YAAK;YACzC;gBAAE,MAAM;gBAAU,OAAO;gBAAI,MAAM;YAAK;YACxC;gBAAE,MAAM;gBAAc,OAAO;gBAAI,MAAM;YAAK;YAC5C;gBAAE,MAAM;gBAAO,OAAO;gBAAI,MAAM;YAAK;YACrC;gBAAE,MAAM;gBAAU,OAAO;gBAAI,MAAM;YAAK;SACzC;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAS,OAAO;gBAAI,MAAM;YAAK;YACvC;gBAAE,MAAM;gBAAY,OAAO;gBAAI,MAAM;YAAK;YAC1C;gBAAE,MAAM;gBAAa,OAAO;gBAAI,MAAM;YAAM;YAC5C;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,MAAM;YAAI;YAC7C;gBAAE,MAAM;gBAAe,OAAO;gBAAI,MAAM;YAAK;SAC9C;IACH;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,qBACE,6LAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,WAAW,YAAY;oBAChC,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS,WAAW;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAChE,YAAY;gDACV,UAAU;gDACV,OAAO,gBAAgB,MAAM,aAAa;4CAC5C;4CACA,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAY,MAAM,IAAI;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EAA8B,MAAM,IAAI;;;;;;;;;;;;sEAE1D,6LAAC;4DAAK,WAAU;;gEAA+B,MAAM,KAAK;gEAAC;;;;;;;;;;;;;8DAG7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO;4DAAE;4DACpB,SAAS,WAAW;gEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;4DAAC,IAAI;gEAAE,OAAO;4DAAE;4DAC9D,YAAY;gEACV,UAAU;gEACV,OAAO,gBAAgB,MAAM,aAAa,MAAM;gEAChD,MAAM;4DACR;4DACA,WAAU;;;;;;sEAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;4DAAE;4DACtB,SAAS,WAAW;gEAAE,SAAS;oEAAC;oEAAG;oEAAG;iEAAE;4DAAC,IAAI;gEAAE,SAAS;4DAAE;4DAC1D,YAAY;gEACV,UAAU;gEACV,OAAO,gBAAgB,MAAM,aAAa,MAAM;4DAClD;4DACA,WAAU;4DACV,OAAO;gEAAE,WAAW,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;4DAAC;;;;;;;;;;;;;2CApCjD,MAAM,IAAI;;;;;;;;;;;2BAXhB,SAAS,KAAK;;;;;;;;;;8BA0DzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAInE,6LAAC;4BAAI,WAAU;sCACZ;gCACC;gCAAc;gCAAS;gCAAW;gCAAc;gCAAW;gCAC3D;gCAAgB;gCAAiB;gCAAc;gCAAW;gCAC1D;gCAAO;gCAAU;gCAAS;gCAAwB;gCAAO;6BAC1D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCAEV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,IAAI,QAAQ;oCAAK;oCACrD,YAAY;wCAAE,OAAO;wCAAK,GAAG,CAAC;oCAAE;oCAChC,WAAU;8CAET;mCAPI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAerB;GA7HgB;;QAEG,gLAAA,CAAA,YAAS;;;KAFZ", "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'framer-motion';\nimport { useRef } from 'react';\nimport { ExternalLink, Github, Eye } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\n\nconst projects = [\n  {\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'A modern, full-stack e-commerce solution with real-time inventory management, secure payments, and an intuitive admin dashboard.',\n    image: '/api/placeholder/600/400',\n    tags: ['Next.js', 'TypeScript', 'Stripe', 'PostgreSQL', 'Tailwind CSS'],\n    liveUrl: 'https://example.com',\n    githubUrl: 'https://github.com/example',\n    featured: true,\n  },\n  {\n    id: 2,\n    title: 'AI-Powered Analytics Dashboard',\n    description: 'Interactive dashboard with machine learning insights, real-time data visualization, and predictive analytics for business intelligence.',\n    image: '/api/placeholder/600/400',\n    tags: ['React', 'Python', 'TensorFlow', 'D3.js', 'FastAPI'],\n    liveUrl: 'https://example.com',\n    githubUrl: 'https://github.com/example',\n    featured: true,\n  },\n  {\n    id: 3,\n    title: 'Social Media App',\n    description: 'A responsive social platform with real-time messaging, content sharing, and advanced privacy controls.',\n    image: '/api/placeholder/600/400',\n    tags: ['React Native', 'Node.js', 'Socket.io', 'MongoDB', 'AWS'],\n    liveUrl: 'https://example.com',\n    githubUrl: 'https://github.com/example',\n    featured: false,\n  },\n  {\n    id: 4,\n    title: 'Design System Library',\n    description: 'Comprehensive component library with documentation, accessibility features, and theme customization.',\n    image: '/api/placeholder/600/400',\n    tags: ['React', 'Storybook', 'TypeScript', 'CSS-in-JS', 'Jest'],\n    liveUrl: 'https://example.com',\n    githubUrl: 'https://github.com/example',\n    featured: false,\n  },\n  {\n    id: 5,\n    title: 'Fitness Tracking App',\n    description: 'Mobile-first fitness application with workout planning, progress tracking, and social features.',\n    image: '/api/placeholder/600/400',\n    tags: ['React Native', 'Firebase', 'Redux', 'Chart.js', 'Expo'],\n    liveUrl: 'https://example.com',\n    githubUrl: 'https://github.com/example',\n    featured: false,\n  },\n  {\n    id: 6,\n    title: 'Portfolio Website',\n    description: 'This very portfolio! A showcase of modern web development with smooth animations and responsive design.',\n    image: '/api/placeholder/600/400',\n    tags: ['Next.js', 'TypeScript', 'Framer Motion', 'Tailwind CSS'],\n    liveUrl: 'https://example.com',\n    githubUrl: 'https://github.com/example',\n    featured: false,\n  },\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n    },\n  },\n};\n\nexport function Projects() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: '-100px' });\n\n  const featuredProjects = projects.filter(project => project.featured);\n  const otherProjects = projects.filter(project => !project.featured);\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-navy/50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\"\n          >\n            Featured Projects\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-slate-300 max-w-3xl mx-auto\"\n          >\n            A collection of projects that showcase my skills in modern web development, \n            UI/UX design, and problem-solving\n          </motion.p>\n        </motion.div>\n\n        {/* Featured Projects */}\n        <motion.div\n          variants={containerVariants}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\"\n        >\n          {featuredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              variants={itemVariants}\n              whileHover={{ y: -10 }}\n              className=\"group glass rounded-2xl overflow-hidden hover:glass-strong transition-all duration-500\"\n            >\n              <div className=\"relative overflow-hidden\">\n                <div className=\"aspect-video bg-gradient-to-br from-teal-500/20 to-cyan-500/20 flex items-center justify-center\">\n                  <Eye className=\"w-16 h-16 text-teal-400/50\" />\n                </div>\n                <div className=\"absolute inset-0 bg-gradient-to-t from-navy/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                <div className=\"absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <motion.a\n                    href={project.liveUrl}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-2 glass rounded-full hover:glass-strong\"\n                  >\n                    <ExternalLink className=\"w-4 h-4 text-white\" />\n                  </motion.a>\n                  <motion.a\n                    href={project.githubUrl}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-2 glass rounded-full hover:glass-strong\"\n                  >\n                    <Github className=\"w-4 h-4 text-white\" />\n                  </motion.a>\n                </div>\n              </div>\n              \n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-3 group-hover:text-teal-400 transition-colors duration-300\">\n                  {project.title}\n                </h3>\n                <p className=\"text-slate-300 mb-4 leading-relaxed\">\n                  {project.description}\n                </p>\n                \n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {project.tags.map((tag) => (\n                    <span\n                      key={tag}\n                      className=\"px-3 py-1 text-xs font-medium bg-teal-500/20 text-teal-400 rounded-full\"\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n                \n                <div className=\"flex space-x-3\">\n                  <Button variant=\"secondary\" size=\"sm\" className=\"flex-1\">\n                    <ExternalLink className=\"w-4 h-4 mr-2\" />\n                    Live Demo\n                  </Button>\n                  <Button variant=\"ghost\" size=\"sm\">\n                    <Github className=\"w-4 h-4 mr-2\" />\n                    Code\n                  </Button>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Other Projects Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 40 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n        >\n          <h3 className=\"text-2xl font-semibold text-center text-white mb-8\">\n            More Projects\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {otherProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}\n                whileHover={{ y: -5, scale: 1.02 }}\n                className=\"group glass rounded-xl p-6 hover:glass-strong transition-all duration-300\"\n              >\n                <div className=\"flex items-start justify-between mb-4\">\n                  <h4 className=\"text-lg font-semibold text-white group-hover:text-teal-400 transition-colors duration-300\">\n                    {project.title}\n                  </h4>\n                  <div className=\"flex space-x-2\">\n                    <motion.a\n                      href={project.liveUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.1 }}\n                      className=\"text-slate-400 hover:text-teal-400 transition-colors duration-200\"\n                    >\n                      <ExternalLink className=\"w-4 h-4\" />\n                    </motion.a>\n                    <motion.a\n                      href={project.githubUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.1 }}\n                      className=\"text-slate-400 hover:text-teal-400 transition-colors duration-200\"\n                    >\n                      <Github className=\"w-4 h-4\" />\n                    </motion.a>\n                  </div>\n                </div>\n                \n                <p className=\"text-slate-300 text-sm mb-4 leading-relaxed\">\n                  {project.description}\n                </p>\n                \n                <div className=\"flex flex-wrap gap-1\">\n                  {project.tags.slice(0, 3).map((tag) => (\n                    <span\n                      key={tag}\n                      className=\"px-2 py-1 text-xs font-medium bg-slate-700/50 text-slate-400 rounded\"\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                  {project.tags.length > 3 && (\n                    <span className=\"px-2 py-1 text-xs font-medium text-slate-500\">\n                      +{project.tags.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAW;YAAc;YAAU;YAAc;SAAe;QACvE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAS;YAAU;YAAc;YAAS;SAAU;QAC3D,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAgB;YAAW;YAAa;YAAW;SAAM;QAChE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAS;YAAa;YAAc;YAAa;SAAO;QAC/D,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAgB;YAAY;YAAS;YAAY;SAAO;QAC/D,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAW;YAAc;YAAiB;SAAe;QAChE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpE,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ;IAElE,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,WAAW,YAAY;oBAChC,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;4BAAG;4BACrB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAM,QAAQ,OAAO;oDACrB,QAAO;oDACP,KAAI;oDACJ,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAM,QAAQ,SAAS;oDACvB,QAAO;oDACP,KAAI;oDACJ,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAGtB,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDAQX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAY,MAAK;oDAAK,WAAU;;sEAC9C,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG3C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;;sEAC3B,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;2BA3DpC,QAAQ,EAAE;;;;;;;;;;8BAqErB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAInE,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC/D,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,YAAY;wCAAE,GAAG,CAAC;wCAAG,OAAO;oCAAK;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,MAAM,QAAQ,OAAO;4DACrB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,WAAU;sEAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,MAAM,QAAQ,SAAS;4DACvB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,WAAU;sEAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAKxB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAGtB,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;gDAMR,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,6LAAC;oDAAK,WAAU;;wDAA+C;wDAC3D,QAAQ,IAAI,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;mCAhD3B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2D/B;GAnLgB;;QAEG,gLAAA,CAAA,YAAS;;;KAFZ", "debugId": null}}, {"offset": {"line": 1979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/Experience.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'framer-motion';\nimport { useRef } from 'react';\nimport { Calendar, MapPin, ExternalLink } from 'lucide-react';\n\nconst experiences = [\n  {\n    id: 1,\n    title: 'Senior Frontend Developer',\n    company: 'TechCorp Solutions',\n    location: 'San Francisco, CA',\n    period: '2022 - Present',\n    description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',\n    achievements: [\n      'Improved application performance by 40% through code optimization',\n      'Led migration from legacy codebase to modern React/TypeScript stack',\n      'Mentored 5+ junior developers and established coding standards',\n      'Implemented automated testing reducing bugs by 60%'\n    ],\n    technologies: ['React', 'TypeScript', 'Next.js', 'GraphQL', 'AWS'],\n    companyUrl: 'https://techcorp.com',\n  },\n  {\n    id: 2,\n    title: 'Full Stack Developer',\n    company: 'StartupXYZ',\n    location: 'Remote',\n    period: '2020 - 2022',\n    description: 'Built scalable web applications from concept to deployment, working closely with design and product teams.',\n    achievements: [\n      'Developed MVP that secured $2M in Series A funding',\n      'Built real-time collaboration features using WebSocket',\n      'Implemented CI/CD pipeline reducing deployment time by 80%',\n      'Designed and developed RESTful APIs serving 10k+ users'\n    ],\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Docker', 'Kubernetes'],\n    companyUrl: 'https://startupxyz.com',\n  },\n  {\n    id: 3,\n    title: 'Frontend Developer',\n    company: 'Digital Agency Pro',\n    location: 'New York, NY',\n    period: '2019 - 2020',\n    description: 'Created responsive websites and web applications for diverse clients, focusing on performance and user experience.',\n    achievements: [\n      'Delivered 20+ client projects with 98% satisfaction rate',\n      'Reduced average page load time by 50% across all projects',\n      'Implemented accessibility standards achieving WCAG 2.1 AA compliance',\n      'Collaborated with UX team to improve conversion rates by 35%'\n    ],\n    technologies: ['JavaScript', 'React', 'SCSS', 'WordPress', 'Figma'],\n    companyUrl: 'https://digitalagencypro.com',\n  },\n  {\n    id: 4,\n    title: 'Junior Web Developer',\n    company: 'WebDev Studio',\n    location: 'Austin, TX',\n    period: '2018 - 2019',\n    description: 'Started my professional journey building websites and learning modern development practices.',\n    achievements: [\n      'Completed 15+ website projects using modern frameworks',\n      'Learned React, Node.js, and modern development workflows',\n      'Contributed to open-source projects and internal tools',\n      'Achieved Google PageSpeed scores of 90+ on all projects'\n    ],\n    technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'Git'],\n    companyUrl: 'https://webdevstudio.com',\n  },\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.3,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, x: -50 },\n  visible: {\n    opacity: 1,\n    x: 0,\n    transition: {\n      duration: 0.8,\n    },\n  },\n};\n\nexport function Experience() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: '-100px' });\n\n  return (\n    <section id=\"experience\" className=\"py-20 bg-slate-900/30\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\"\n          >\n            Professional Experience\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-slate-300 max-w-3xl mx-auto\"\n          >\n            My journey through the tech industry, building innovative solutions \n            and growing as a developer\n          </motion.p>\n        </motion.div>\n\n        <div className=\"relative\">\n          {/* Timeline Line */}\n          <div className=\"absolute left-8 md:left-1/2 transform md:-translate-x-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-teal-500 via-cyan-400 to-teal-500\"></div>\n\n          <motion.div\n            variants={containerVariants}\n            className=\"space-y-12\"\n          >\n            {experiences.map((experience, index) => (\n              <motion.div\n                key={experience.id}\n                variants={itemVariants}\n                className={`relative flex items-center ${\n                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'\n                } flex-col md:space-x-8`}\n              >\n                {/* Timeline Dot */}\n                <div className=\"absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-teal-400 rounded-full border-4 border-navy z-10 shadow-lg shadow-teal-400/50\"></div>\n\n                {/* Content Card */}\n                <motion.div\n                  whileHover={{ scale: 1.02, y: -5 }}\n                  className={`w-full md:w-5/12 ml-16 md:ml-0 ${\n                    index % 2 === 0 ? 'md:mr-auto' : 'md:ml-auto'\n                  }`}\n                >\n                  <div className=\"glass rounded-2xl p-6 hover:glass-strong transition-all duration-300\">\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div>\n                        <h3 className=\"text-xl font-semibold text-white mb-1\">\n                          {experience.title}\n                        </h3>\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <motion.a\n                            href={experience.companyUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-teal-400 font-medium hover:text-teal-300 transition-colors duration-200 flex items-center space-x-1\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <span>{experience.company}</span>\n                            <ExternalLink className=\"w-3 h-3\" />\n                          </motion.a>\n                        </div>\n                        <div className=\"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-slate-400\">\n                          <div className=\"flex items-center space-x-1\">\n                            <Calendar className=\"w-4 h-4\" />\n                            <span>{experience.period}</span>\n                          </div>\n                          <div className=\"flex items-center space-x-1\">\n                            <MapPin className=\"w-4 h-4\" />\n                            <span>{experience.location}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <p className=\"text-slate-300 mb-4 leading-relaxed\">\n                      {experience.description}\n                    </p>\n\n                    <div className=\"mb-4\">\n                      <h4 className=\"text-sm font-semibold text-white mb-2\">Key Achievements:</h4>\n                      <ul className=\"space-y-1\">\n                        {experience.achievements.map((achievement, achievementIndex) => (\n                          <motion.li\n                            key={achievementIndex}\n                            initial={{ opacity: 0, x: -10 }}\n                            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}\n                            transition={{ duration: 0.4, delay: index * 0.3 + achievementIndex * 0.1 }}\n                            className=\"text-sm text-slate-300 flex items-start space-x-2\"\n                          >\n                            <span className=\"text-teal-400 mt-1\">•</span>\n                            <span>{achievement}</span>\n                          </motion.li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <div>\n                      <h4 className=\"text-sm font-semibold text-white mb-2\">Technologies:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {experience.technologies.map((tech, techIndex) => (\n                          <motion.span\n                            key={tech}\n                            initial={{ opacity: 0, scale: 0.8 }}\n                            animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}\n                            transition={{ duration: 0.3, delay: index * 0.3 + techIndex * 0.05 }}\n                            className=\"px-2 py-1 text-xs font-medium bg-teal-500/20 text-teal-400 rounded\"\n                          >\n                            {tech}\n                          </motion.span>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 40 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}\n          transition={{ duration: 0.8, delay: 1.2 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"glass rounded-2xl p-8 max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-semibold text-white mb-4\">\n              Ready to Work Together?\n            </h3>\n            <p className=\"text-slate-300 mb-6\">\n              I'm always interested in new opportunities and exciting projects. \n              Let's discuss how we can bring your ideas to life.\n            </p>\n            <motion.a\n              href=\"#contact\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-teal-500 to-cyan-400 text-navy font-semibold rounded-lg hover:from-teal-400 hover:to-cyan-300 transition-all duration-300 neon-glow\"\n            >\n              Get In Touch\n            </motion.a>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAOA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAc;YAAW;YAAW;SAAM;QAClE,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;SAAa;QACxE,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAc;YAAS;YAAQ;YAAa;SAAQ;QACnE,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAO;YAAc;YAAS;SAAM;QAC3D,YAAY;IACd;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC7B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,qBACE,6LAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,WAAW,YAAY;oBAChC,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,WAAW,CAAC,2BAA2B,EACrC,QAAQ,MAAM,IAAI,gBAAgB,sBACnC,sBAAsB,CAAC;;sDAGxB,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;gDAAM,GAAG,CAAC;4CAAE;4CACjC,WAAW,CAAC,+BAA+B,EACzC,QAAQ,MAAM,IAAI,eAAe,cACjC;sDAEF,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,WAAW,KAAK;;;;;;8EAEnB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wEACP,MAAM,WAAW,UAAU;wEAC3B,QAAO;wEACP,KAAI;wEACJ,WAAU;wEACV,YAAY;4EAAE,OAAO;wEAAK;;0FAE1B,6LAAC;0FAAM,WAAW,OAAO;;;;;;0FACzB,6LAAC,yNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAG5B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,6LAAC;8FAAM,WAAW,MAAM;;;;;;;;;;;;sFAE1B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,6LAAC;8FAAM,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAMlC,6LAAC;wDAAE,WAAU;kEACV,WAAW,WAAW;;;;;;kEAGzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAwC;;;;;;0EACtD,6LAAC;gEAAG,WAAU;0EACX,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,iCACzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wEAER,SAAS;4EAAE,SAAS;4EAAG,GAAG,CAAC;wEAAG;wEAC9B,SAAS,WAAW;4EAAE,SAAS;4EAAG,GAAG;wEAAE,IAAI;4EAAE,SAAS;4EAAG,GAAG,CAAC;wEAAG;wEAChE,YAAY;4EAAE,UAAU;4EAAK,OAAO,QAAQ,MAAM,mBAAmB;wEAAI;wEACzE,WAAU;;0FAEV,6LAAC;gFAAK,WAAU;0FAAqB;;;;;;0FACrC,6LAAC;0FAAM;;;;;;;uEAPF;;;;;;;;;;;;;;;;kEAab,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAwC;;;;;;0EACtD,6LAAC;gEAAI,WAAU;0EACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,0BAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wEAEV,SAAS;4EAAE,SAAS;4EAAG,OAAO;wEAAI;wEAClC,SAAS,WAAW;4EAAE,SAAS;4EAAG,OAAO;wEAAE,IAAI;4EAAE,SAAS;4EAAG,OAAO;wEAAI;wEACxE,YAAY;4EAAE,UAAU;4EAAK,OAAO,QAAQ,MAAM,YAAY;wEAAK;wEACnE,WAAU;kFAET;uEANI;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA1EZ,WAAW,EAAE;;;;;;;;;;;;;;;;8BA6F1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAInC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,MAAK;gCACL,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA/JgB;;QAEG,gLAAA,CAAA,YAAS;;;KAFZ", "debugId": null}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/YouTube.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'framer-motion';\nimport { Play, ExternalLink, Calendar, Eye } from 'lucide-react';\n\ninterface YouTubeVideo {\n  id: string;\n  title: string;\n  description: string;\n  thumbnail: string;\n  publishedAt: string;\n  viewCount?: string;\n  duration?: string;\n  url: string;\n}\n\n// Mock data for demonstration - replace with actual API call\nconst mockVideos: YouTubeVideo[] = [\n  {\n    id: '1',\n    title: 'Building a Modern React Portfolio with Next.js and Framer Motion',\n    description: 'Learn how to create a stunning portfolio website using the latest web technologies including Next.js, TypeScript, and Framer Motion animations.',\n    thumbnail: '/api/placeholder/480/360',\n    publishedAt: '2024-01-15',\n    viewCount: '12,543',\n    duration: '24:15',\n    url: 'https://youtube.com/watch?v=example1',\n  },\n  {\n    id: '2',\n    title: 'Advanced TypeScript Patterns for React Developers',\n    description: 'Dive deep into advanced TypeScript patterns that will make your React code more robust, maintainable, and type-safe.',\n    thumbnail: '/api/placeholder/480/360',\n    publishedAt: '2024-01-08',\n    viewCount: '8,921',\n    duration: '18:42',\n    url: 'https://youtube.com/watch?v=example2',\n  },\n  {\n    id: '3',\n    title: 'CSS Grid vs Flexbox: When to Use Which?',\n    description: 'A comprehensive comparison of CSS Grid and Flexbox with practical examples and real-world use cases.',\n    thumbnail: '/api/placeholder/480/360',\n    publishedAt: '2024-01-01',\n    viewCount: '15,672',\n    duration: '16:28',\n    url: 'https://youtube.com/watch?v=example3',\n  },\n  {\n    id: '4',\n    title: 'Building Responsive Layouts with Tailwind CSS',\n    description: 'Master responsive design with Tailwind CSS utility classes and create beautiful, mobile-first layouts.',\n    thumbnail: '/api/placeholder/480/360',\n    publishedAt: '2023-12-25',\n    viewCount: '9,834',\n    duration: '21:33',\n    url: 'https://youtube.com/watch?v=example4',\n  },\n  {\n    id: '5',\n    title: 'State Management in React: Redux vs Zustand vs Context',\n    description: 'Compare different state management solutions for React applications and learn when to use each approach.',\n    thumbnail: '/api/placeholder/480/360',\n    publishedAt: '2023-12-18',\n    viewCount: '11,205',\n    duration: '28:17',\n    url: 'https://youtube.com/watch?v=example5',\n  },\n  {\n    id: '6',\n    title: 'Optimizing React Performance: Tips and Tricks',\n    description: 'Learn essential techniques to optimize your React applications for better performance and user experience.',\n    thumbnail: '/api/placeholder/480/360',\n    publishedAt: '2023-12-11',\n    viewCount: '7,456',\n    duration: '19:52',\n    url: 'https://youtube.com/watch?v=example6',\n  },\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n    },\n  },\n};\n\nexport function YouTube() {\n  const [videos, setVideos] = useState<YouTubeVideo[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: '-100px' });\n\n  useEffect(() => {\n    // Simulate API call - replace with actual YouTube API integration\n    const fetchVideos = async () => {\n      try {\n        setLoading(true);\n        // Simulate network delay\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setVideos(mockVideos);\n      } catch (err) {\n        setError('Failed to load videos');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (isInView) {\n      fetchVideos();\n    }\n  }, [isInView]);\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const formatViewCount = (count: string) => {\n    return count;\n  };\n\n  return (\n    <section id=\"youtube\" className=\"py-20 bg-navy/30\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\"\n          >\n            Latest YouTube Videos\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-slate-300 max-w-3xl mx-auto\"\n          >\n            Sharing knowledge through tutorials, tips, and insights about \n            modern web development and design\n          </motion.p>\n        </motion.div>\n\n        {loading && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[...Array(6)].map((_, index) => (\n              <div key={index} className=\"glass rounded-2xl overflow-hidden animate-pulse\">\n                <div className=\"aspect-video bg-slate-700\"></div>\n                <div className=\"p-6 space-y-3\">\n                  <div className=\"h-4 bg-slate-700 rounded w-3/4\"></div>\n                  <div className=\"h-3 bg-slate-700 rounded w-full\"></div>\n                  <div className=\"h-3 bg-slate-700 rounded w-2/3\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {error && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center py-12\"\n          >\n            <div className=\"glass rounded-2xl p-8 max-w-md mx-auto\">\n              <p className=\"text-red-400 mb-4\">{error}</p>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"px-4 py-2 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors duration-200\"\n              >\n                Try Again\n              </button>\n            </div>\n          </motion.div>\n        )}\n\n        {!loading && !error && (\n          <motion.div\n            variants={containerVariants}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n          >\n            {videos.map((video, index) => (\n              <motion.div\n                key={video.id}\n                variants={itemVariants}\n                whileHover={{ y: -10, scale: 1.02 }}\n                className=\"group glass rounded-2xl overflow-hidden hover:glass-strong transition-all duration-500\"\n              >\n                <div className=\"relative aspect-video overflow-hidden\">\n                  <div className=\"w-full h-full bg-gradient-to-br from-teal-500/20 to-cyan-500/20 flex items-center justify-center\">\n                    <Play className=\"w-16 h-16 text-teal-400/50\" />\n                  </div>\n                  \n                  {/* Play Button Overlay */}\n                  <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                    <motion.div\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"w-16 h-16 bg-red-600 rounded-full flex items-center justify-center cursor-pointer\"\n                    >\n                      <Play className=\"w-6 h-6 text-white ml-1\" fill=\"currentColor\" />\n                    </motion.div>\n                  </div>\n\n                  {/* Duration Badge */}\n                  {video.duration && (\n                    <div className=\"absolute bottom-2 right-2 px-2 py-1 bg-black/80 text-white text-xs rounded\">\n                      {video.duration}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-lg font-semibold text-white mb-2 line-clamp-2 group-hover:text-teal-400 transition-colors duration-300\">\n                    {video.title}\n                  </h3>\n                  \n                  <p className=\"text-slate-300 text-sm mb-4 line-clamp-3 leading-relaxed\">\n                    {video.description}\n                  </p>\n\n                  <div className=\"flex items-center justify-between text-xs text-slate-400 mb-4\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Calendar className=\"w-3 h-3\" />\n                      <span>{formatDate(video.publishedAt)}</span>\n                    </div>\n                    {video.viewCount && (\n                      <div className=\"flex items-center space-x-1\">\n                        <Eye className=\"w-3 h-3\" />\n                        <span>{formatViewCount(video.viewCount)} views</span>\n                      </div>\n                    )}\n                  </div>\n\n                  <motion.a\n                    href={video.url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"inline-flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200\"\n                  >\n                    <Play className=\"w-4 h-4\" />\n                    <span>Watch Video</span>\n                    <ExternalLink className=\"w-3 h-3\" />\n                  </motion.a>\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n\n        {/* Channel CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 40 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}\n          transition={{ duration: 0.8, delay: 1 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"glass rounded-2xl p-8 max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-semibold text-white mb-4\">\n              Subscribe for More Content\n            </h3>\n            <p className=\"text-slate-300 mb-6\">\n              Join thousands of developers learning modern web development. \n              New tutorials every week covering React, TypeScript, and more!\n            </p>\n            <motion.a\n              href=\"https://youtube.com/@yourchannel\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"inline-flex items-center space-x-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-colors duration-300\"\n            >\n              <Play className=\"w-5 h-5\" />\n              <span>Subscribe on YouTube</span>\n              <ExternalLink className=\"w-4 h-4\" />\n            </motion.a>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAkBA,6DAA6D;AAC7D,MAAM,aAA6B;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,KAAK;IACP;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,kEAAkE;YAClE,MAAM;iDAAc;oBAClB,IAAI;wBACF,WAAW;wBACX,yBAAyB;wBACzB,MAAM,IAAI;6DAAQ,CAAA,UAAW,WAAW,SAAS;;wBACjD,UAAU;oBACZ,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,UAAU;gBACZ;YACF;QACF;4BAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,WAAW,YAAY;oBAChC,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;gBAMF,yBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;2BALT;;;;;;;;;;gBAYf,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;;;;;;;;;;;;gBAON,CAAC,WAAW,CAAC,uBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BAClC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIlB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;oDAA0B,MAAK;;;;;;;;;;;;;;;;wCAKlD,MAAM,QAAQ,kBACb,6LAAC;4CAAI,WAAU;sDACZ,MAAM,QAAQ;;;;;;;;;;;;8CAKrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAGd,6LAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAGpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAM,WAAW,MAAM,WAAW;;;;;;;;;;;;gDAEpC,MAAM,SAAS,kBACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;;gEAAM,gBAAgB,MAAM,SAAS;gEAAE;;;;;;;;;;;;;;;;;;;sDAK9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAM,MAAM,GAAG;4CACf,QAAO;4CACP,KAAI;4CACJ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;8DACN,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BA7DvB,MAAM,EAAE;;;;;;;;;;8BAsErB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;oBACtC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAInC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GA7MgB;;QAKG,gLAAA,CAAA,YAAS;;;KALZ", "debugId": null}}, {"offset": {"line": 3139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'framer-motion';\nimport { Mail, Phone, MapPin, Send, CheckCircle, AlertCircle } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\n\ninterface FormData {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\ninterface FormStatus {\n  type: 'idle' | 'loading' | 'success' | 'error';\n  message: string;\n}\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n    },\n  },\n};\n\nexport function Contact() {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n  });\n  const [status, setStatus] = useState<FormStatus>({\n    type: 'idle',\n    message: '',\n  });\n\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: '-100px' });\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setStatus({ type: 'loading', message: 'Sending message...' });\n\n    try {\n      // Simulate form submission - replace with actual API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Mock success response\n      setStatus({\n        type: 'success',\n        message: 'Thank you! Your message has been sent successfully.',\n      });\n      \n      // Reset form\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: '',\n      });\n    } catch (error) {\n      setStatus({\n        type: 'error',\n        message: 'Sorry, there was an error sending your message. Please try again.',\n      });\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>',\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+****************',\n      href: 'tel:+15551234567',\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'San Francisco, CA',\n      href: 'https://maps.google.com/?q=San+Francisco,+CA',\n    },\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-slate-900/50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\"\n          >\n            Let's Work Together\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-slate-300 max-w-3xl mx-auto\"\n          >\n            Have a project in mind or want to discuss opportunities? \n            I'd love to hear from you and bring your ideas to life.\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            variants={itemVariants}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-semibold text-white mb-6\">\n                Get in Touch\n              </h3>\n              <p className=\"text-slate-300 leading-relaxed mb-8\">\n                I'm always open to discussing new projects, creative ideas, or \n                opportunities to be part of your vision. Whether you're a startup \n                looking to build your first product or an established company \n                wanting to innovate, let's connect!\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {contactInfo.map((info, index) => (\n                <motion.a\n                  key={info.label}\n                  href={info.href}\n                  target={info.href.startsWith('http') ? '_blank' : undefined}\n                  rel={info.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}\n                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}\n                  whileHover={{ x: 10 }}\n                  className=\"flex items-center space-x-4 p-4 glass rounded-xl hover:glass-strong transition-all duration-300 group\"\n                >\n                  <div className=\"w-12 h-12 bg-teal-500/20 rounded-full flex items-center justify-center group-hover:bg-teal-500/30 transition-colors duration-300\">\n                    <info.icon className=\"w-6 h-6 text-teal-400\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-slate-400 font-medium\">{info.label}</p>\n                    <p className=\"text-white group-hover:text-teal-400 transition-colors duration-300\">\n                      {info.value}\n                    </p>\n                  </div>\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              className=\"pt-8\"\n            >\n              <h4 className=\"text-lg font-semibold text-white mb-4\">\n                Follow Me\n              </h4>\n              <div className=\"flex space-x-4\">\n                {[\n                  { name: 'GitHub', url: 'https://github.com' },\n                  { name: 'LinkedIn', url: 'https://linkedin.com' },\n                  { name: 'Twitter', url: 'https://twitter.com' },\n                  { name: 'Dribbble', url: 'https://dribbble.com' },\n                ].map((social) => (\n                  <motion.a\n                    key={social.name}\n                    href={social.url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.1, y: -2 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 glass rounded-lg hover:glass-strong transition-all duration-300 text-slate-300 hover:text-teal-400\"\n                  >\n                    {social.name}\n                  </motion.a>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            variants={itemVariants}\n            className=\"glass rounded-2xl p-8\"\n          >\n            <h3 className=\"text-2xl font-semibold text-white mb-6\">\n              Send a Message\n            </h3>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-slate-300 mb-2\">\n                    Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    required\n                    className=\"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200\"\n                    placeholder=\"Your name\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-slate-300 mb-2\">\n                    Email *\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    required\n                    className=\"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Subject *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200\"\n                  placeholder=\"What's this about?\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Message *\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleInputChange}\n                  required\n                  rows={6}\n                  className=\"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200 resize-none\"\n                  placeholder=\"Tell me about your project or idea...\"\n                />\n              </div>\n\n              {/* Status Message */}\n              {status.type !== 'idle' && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className={`flex items-center space-x-2 p-4 rounded-lg ${\n                    status.type === 'success'\n                      ? 'bg-green-500/20 text-green-400 border border-green-500/30'\n                      : status.type === 'error'\n                      ? 'bg-red-500/20 text-red-400 border border-red-500/30'\n                      : 'bg-teal-500/20 text-teal-400 border border-teal-500/30'\n                  }`}\n                >\n                  {status.type === 'success' && <CheckCircle className=\"w-5 h-5\" />}\n                  {status.type === 'error' && <AlertCircle className=\"w-5 h-5\" />}\n                  {status.type === 'loading' && (\n                    <div className=\"w-5 h-5 border-2 border-teal-400 border-t-transparent rounded-full animate-spin\" />\n                  )}\n                  <span>{status.message}</span>\n                </motion.div>\n              )}\n\n              <Button\n                type=\"submit\"\n                variant=\"neon\"\n                size=\"lg\"\n                className=\"w-full\"\n                disabled={status.type === 'loading'}\n              >\n                {status.type === 'loading' ? (\n                  <>\n                    <div className=\"w-5 h-5 border-2 border-navy border-t-transparent rounded-full animate-spin mr-2\" />\n                    Sending...\n                  </>\n                ) : (\n                  <>\n                    <Send className=\"w-5 h-5 mr-2\" />\n                    Send Message\n                  </>\n                )}\n              </Button>\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAoBA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAC/C,MAAM;QACN,SAAS;IACX;IAEA,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,UAAU;YAAE,MAAM;YAAW,SAAS;QAAqB;QAE3D,IAAI;YACF,0DAA0D;YAC1D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,wBAAwB;YACxB,UAAU;gBACR,MAAM;gBACN,SAAS;YACX;YAEA,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,UAAU;gBACR,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,WAAW,YAAY;oBAChC,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAQrD,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,KAAK,IAAI;4CACf,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;4CAClD,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;4CAC5D,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS,WAAW;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAChE,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,YAAY;gDAAE,GAAG;4CAAG;4CACpB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAsC,KAAK,KAAK;;;;;;sEAC7D,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;2CAhBV,KAAK,KAAK;;;;;;;;;;8CAwBrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC/D,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,MAAM;oDAAU,KAAK;gDAAqB;gDAC5C;oDAAE,MAAM;oDAAY,KAAK;gDAAuB;gDAChD;oDAAE,MAAM;oDAAW,KAAK;gDAAsB;gDAC9C;oDAAE,MAAM;oDAAY,KAAK;gDAAuB;6CACjD,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,GAAG;oDAChB,QAAO;oDACP,KAAI;oDACJ,YAAY;wDAAE,OAAO;wDAAK,GAAG,CAAC;oDAAE;oDAChC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAET,OAAO,IAAI;mDARP,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAgB1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAIvD,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAgD;;;;;;sEAGhF,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAAgD;;;;;;sEAGjF,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAAgD;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAAgD;;;;;;8DAGnF,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;oDACR,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;wCAKf,OAAO,IAAI,KAAK,wBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,WAAW,CAAC,2CAA2C,EACrD,OAAO,IAAI,KAAK,YACZ,8DACA,OAAO,IAAI,KAAK,UAChB,wDACA,0DACJ;;gDAED,OAAO,IAAI,KAAK,2BAAa,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACpD,OAAO,IAAI,KAAK,yBAAW,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAClD,OAAO,IAAI,KAAK,2BACf,6LAAC;oDAAI,WAAU;;;;;;8DAEjB,6LAAC;8DAAM,OAAO,OAAO;;;;;;;;;;;;sDAIzB,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,UAAU,OAAO,IAAI,KAAK;sDAEzB,OAAO,IAAI,KAAK,0BACf;;kEACE,6LAAC;wDAAI,WAAU;;;;;;oDAAqF;;6EAItG;;kEACE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GA3SgB;;QAaG,gLAAA,CAAA,YAAS;;;KAbZ", "debugId": null}}, {"offset": {"line": 3755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Heart, ArrowUp, Github, Linkedin, Twitter, Mail } from 'lucide-react';\nimport { ThemeToggle } from '@/components/ui/ThemeToggle';\n\nconst socialLinks = [\n  {\n    name: 'GitHub',\n    href: 'https://github.com',\n    icon: Github,\n  },\n  {\n    name: 'LinkedIn',\n    href: 'https://linkedin.com',\n    icon: Linkedin,\n  },\n  {\n    name: 'Twitter',\n    href: 'https://twitter.com',\n    icon: Twitter,\n  },\n  {\n    name: 'Email',\n    href: 'mailto:<EMAIL>',\n    icon: Mail,\n  },\n];\n\nconst quickLinks = [\n  { name: 'Home', href: '#home' },\n  { name: 'About', href: '#about' },\n  { name: 'Skills', href: '#skills' },\n  { name: 'Projects', href: '#projects' },\n  { name: 'YouTube', href: '#youtube' },\n  { name: 'Contact', href: '#contact' },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const scrollToSection = (href: string) => {\n    const element = document.getElementById(href.slice(1));\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <footer className=\"relative bg-navy border-t border-slate-800\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(20,184,166,0.05),transparent_50%)]\" />\n      \n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-2xl font-display font-bold gradient-text mb-4\">\n                Alex Johnson\n              </h3>\n              <p className=\"text-slate-300 leading-relaxed mb-6 max-w-md\">\n                Creative Frontend Developer & UI/UX Designer passionate about \n                building exceptional digital experiences with modern technologies \n                and innovative design.\n              </p>\n              <div className=\"flex items-center space-x-4\">\n                <ThemeToggle />\n                <span className=\"text-sm text-slate-400\">\n                  Toggle theme\n                </span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold text-white mb-4\">\n                Quick Links\n              </h4>\n              <ul className=\"space-y-2\">\n                {quickLinks.map((link) => (\n                  <li key={link.name}>\n                    <motion.button\n                      onClick={() => scrollToSection(link.href)}\n                      whileHover={{ x: 5 }}\n                      className=\"text-slate-400 hover:text-teal-400 transition-colors duration-200 text-left\"\n                    >\n                      {link.name}\n                    </motion.button>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n\n          {/* Social Links */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold text-white mb-4\">\n                Connect\n              </h4>\n              <div className=\"space-y-3\">\n                {socialLinks.map((social) => (\n                  <motion.a\n                    key={social.name}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ x: 5 }}\n                    className=\"flex items-center space-x-3 text-slate-400 hover:text-teal-400 transition-colors duration-200\"\n                  >\n                    <social.icon className=\"w-5 h-5\" />\n                    <span>{social.name}</span>\n                  </motion.a>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"py-6 border-t border-slate-800\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <motion.div\n              initial={{ opacity: 0 }}\n              whileInView={{ opacity: 1 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"flex items-center space-x-2 text-slate-400\"\n            >\n              <span>© {new Date().getFullYear()} Alex Johnson. Made with</span>\n              <motion.div\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity, repeatDelay: 2 }}\n              >\n                <Heart className=\"w-4 h-4 text-red-400 fill-current\" />\n              </motion.div>\n              <span>and lots of coffee</span>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              whileInView={{ opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"flex items-center space-x-6\"\n            >\n              <div className=\"text-sm text-slate-400\">\n                Built with Next.js, TypeScript & Tailwind CSS\n              </div>\n              \n              <motion.button\n                onClick={scrollToTop}\n                whileHover={{ scale: 1.1, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"p-2 glass rounded-full hover:glass-strong transition-all duration-300 group\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"w-5 h-5 text-slate-400 group-hover:text-teal-400 transition-colors duration-300\" />\n              </motion.button>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-teal-500 to-transparent opacity-50\" />\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;IACZ;CACD;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAU,MAAM;IAAU;IAClC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,KAAK,CAAC;QACnD,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC;4CAAG,WAAU;sDAAqD;;;;;;sDAGnE,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAK5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,0IAAA,CAAA,cAAW;;;;;8DACZ,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;0CAQ/C,6LAAC;0CACC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAG,WAAU;sDACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;8DACC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wDACxC,YAAY;4DAAE,GAAG;wDAAE;wDACnB,WAAU;kEAET,KAAK,IAAI;;;;;;mDANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0CAe1B,6LAAC;0CACC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,YAAY;wDAAE,GAAG;oDAAE;oDACnB,WAAU;;sEAEV,6LAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;sEACvB,6LAAC;sEAAM,OAAO,IAAI;;;;;;;mDARb,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiB5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,aAAa;wCAAE,SAAS;oCAAE;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;;gDAAK;gDAAG,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,aAAa;4CAAE;sDAE5D,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,aAAa;wCAAE,SAAS;oCAAE;oCAC1B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAyB;;;;;;sDAIxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,YAAY;gDAAE,OAAO;gDAAK,GAAG,CAAC;4CAAE;4CAChC,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7B,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAvJgB", "debugId": null}}, {"offset": {"line": 4224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LoadingScreenProps {\n  onComplete: () => void;\n}\n\nexport function LoadingScreen({ onComplete }: LoadingScreenProps) {\n  const [progress, setProgress] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setProgress((prev) => {\n        if (prev >= 100) {\n          clearInterval(timer);\n          setTimeout(() => {\n            setIsComplete(true);\n            setTimeout(onComplete, 500);\n          }, 500);\n          return 100;\n        }\n        return prev + Math.random() * 15;\n      });\n    }, 100);\n\n    return () => clearInterval(timer);\n  }, [onComplete]);\n\n  return (\n    <AnimatePresence>\n      {!isComplete && (\n        <motion.div\n          initial={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"fixed inset-0 z-50 bg-navy flex items-center justify-center\"\n        >\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(20,184,166,0.1),transparent_50%)]\" />\n\n          {/* Animated Particles */}\n          <div className=\"absolute inset-0\">\n            {[...Array(20)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-1 h-1 bg-teal-400/30 rounded-full\"\n                initial={{\n                  x: Math.random() * 1200,\n                  y: Math.random() * 800,\n                }}\n                animate={{\n                  x: Math.random() * 1200,\n                  y: Math.random() * 800,\n                }}\n                transition={{\n                  duration: Math.random() * 10 + 5,\n                  repeat: Infinity,\n                  repeatType: 'reverse',\n                }}\n              />\n            ))}\n          </div>\n\n          <div className=\"relative z-10 text-center\">\n            {/* Logo/Brand */}\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ duration: 0.8 }}\n              className=\"mb-8\"\n            >\n              <h1 className=\"text-4xl md:text-6xl font-display font-bold gradient-text\">\n                Portfolio\n              </h1>\n              <motion.div\n                initial={{ width: 0 }}\n                animate={{ width: '100%' }}\n                transition={{ duration: 1, delay: 0.5 }}\n                className=\"h-1 bg-gradient-to-r from-teal-500 to-cyan-400 mx-auto mt-4\"\n              />\n            </motion.div>\n\n            {/* Loading Animation */}\n            <div className=\"space-y-6\">\n              {/* Circular Progress */}\n              <div className=\"relative w-24 h-24 mx-auto\">\n                <svg className=\"w-24 h-24 transform -rotate-90\" viewBox=\"0 0 100 100\">\n                  <circle\n                    cx=\"50\"\n                    cy=\"50\"\n                    r=\"40\"\n                    stroke=\"rgba(148, 163, 184, 0.2)\"\n                    strokeWidth=\"8\"\n                    fill=\"none\"\n                  />\n                  <motion.circle\n                    cx=\"50\"\n                    cy=\"50\"\n                    r=\"40\"\n                    stroke=\"url(#gradient)\"\n                    strokeWidth=\"8\"\n                    fill=\"none\"\n                    strokeLinecap=\"round\"\n                    initial={{ pathLength: 0 }}\n                    animate={{ pathLength: progress / 100 }}\n                    transition={{ duration: 0.3 }}\n                    style={{\n                      strokeDasharray: '251.2',\n                      strokeDashoffset: 251.2 * (1 - progress / 100),\n                    }}\n                  />\n                  <defs>\n                    <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                      <stop offset=\"0%\" stopColor=\"#14b8a6\" />\n                      <stop offset=\"100%\" stopColor=\"#00ffff\" />\n                    </linearGradient>\n                  </defs>\n                </svg>\n\n                {/* Progress Text */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <motion.span\n                    key={Math.floor(progress)}\n                    initial={{ scale: 0.8, opacity: 0 }}\n                    animate={{ scale: 1, opacity: 1 }}\n                    className=\"text-xl font-semibold text-white\"\n                  >\n                    {Math.floor(progress)}%\n                  </motion.span>\n                </div>\n              </div>\n\n              {/* Loading Text */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.8 }}\n                className=\"space-y-2\"\n              >\n                <p className=\"text-slate-300 text-lg\">\n                  Loading amazing experiences...\n                </p>\n\n                {/* Animated Dots */}\n                <div className=\"flex justify-center space-x-1\">\n                  {[0, 1, 2].map((i) => (\n                    <motion.div\n                      key={i}\n                      className=\"w-2 h-2 bg-teal-400 rounded-full\"\n                      animate={{\n                        scale: [1, 1.5, 1],\n                        opacity: [0.5, 1, 0.5],\n                      }}\n                      transition={{\n                        duration: 1,\n                        repeat: Infinity,\n                        delay: i * 0.2,\n                      }}\n                    />\n                  ))}\n                </div>\n              </motion.div>\n\n              {/* Progress Bar */}\n              <div className=\"w-64 mx-auto\">\n                <div className=\"h-1 bg-slate-700 rounded-full overflow-hidden\">\n                  <motion.div\n                    className=\"h-full bg-gradient-to-r from-teal-500 to-cyan-400\"\n                    initial={{ width: 0 }}\n                    animate={{ width: `${progress}%` }}\n                    transition={{ duration: 0.3 }}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Loading Messages */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.6, delay: 1.2 }}\n              className=\"mt-8\"\n            >\n              <motion.p\n                key={Math.floor(progress / 25)}\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -10 }}\n                className=\"text-sm text-slate-400\"\n              >\n                {progress < 25 && \"Initializing components...\"}\n                {progress >= 25 && progress < 50 && \"Loading animations...\"}\n                {progress >= 50 && progress < 75 && \"Preparing content...\"}\n                {progress >= 75 && progress < 100 && \"Almost ready...\"}\n                {progress >= 100 && \"Welcome!\"}\n              </motion.p>\n            </motion.div>\n          </div>\n\n          {/* Completion Animation */}\n          {progress >= 100 && (\n            <motion.div\n              initial={{ scale: 0, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              className=\"absolute inset-0 flex items-center justify-center\"\n            >\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: [0, 1.2, 1] }}\n                transition={{ duration: 0.6 }}\n                className=\"w-32 h-32 border-4 border-teal-400 rounded-full flex items-center justify-center\"\n              >\n                <motion.div\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  transition={{ duration: 0.3, delay: 0.3 }}\n                  className=\"text-4xl\"\n                >\n                  ✨\n                </motion.div>\n              </motion.div>\n            </motion.div>\n          )}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AASO,SAAS,cAAc,EAAE,UAAU,EAAsB;;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ;iDAAY;oBACxB;yDAAY,CAAC;4BACX,IAAI,QAAQ,KAAK;gCACf,cAAc;gCACd;qEAAW;wCACT,cAAc;wCACd,WAAW,YAAY;oCACzB;oEAAG;gCACH,OAAO;4BACT;4BACA,OAAO,OAAO,KAAK,MAAM,KAAK;wBAChC;;gBACF;gDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;KAAW;IAEf,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,CAAC,4BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAGV,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCACP,GAAG,KAAK,MAAM,KAAK;gCACnB,GAAG,KAAK,MAAM,KAAK;4BACrB;4BACA,SAAS;gCACP,GAAG,KAAK,MAAM,KAAK;gCACnB,GAAG,KAAK,MAAM,KAAK;4BACrB;4BACA,YAAY;gCACV,UAAU,KAAK,MAAM,KAAK,KAAK;gCAC/B,QAAQ;gCACR,YAAY;4BACd;2BAdK;;;;;;;;;;8BAmBX,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAO;oCACzB,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAI;oCACtC,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAiC,SAAQ;;8DACtD,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,GAAE;oDACF,QAAO;oDACP,aAAY;oDACZ,MAAK;;;;;;8DAEP,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,IAAG;oDACH,IAAG;oDACH,GAAE;oDACF,QAAO;oDACP,aAAY;oDACZ,MAAK;oDACL,eAAc;oDACd,SAAS;wDAAE,YAAY;oDAAE;oDACzB,SAAS;wDAAE,YAAY,WAAW;oDAAI;oDACtC,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,OAAO;wDACL,iBAAiB;wDACjB,kBAAkB,QAAQ,CAAC,IAAI,WAAW,GAAG;oDAC/C;;;;;;8DAEF,6LAAC;8DACC,cAAA,6LAAC;wDAAe,IAAG;wDAAW,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAO,IAAG;;0EACzD,6LAAC;gEAAK,QAAO;gEAAK,WAAU;;;;;;0EAC5B,6LAAC;gEAAK,QAAO;gEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMpC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gDAEV,SAAS;oDAAE,OAAO;oDAAK,SAAS;gDAAE;gDAClC,SAAS;oDAAE,OAAO;oDAAG,SAAS;gDAAE;gDAChC,WAAU;;oDAET,KAAK,KAAK,CAAC;oDAAU;;+CALjB,KAAK,KAAK,CAAC;;;;;;;;;;;;;;;;8CAWtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;sDAKtC,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;4DAAK;yDAAE;wDAClB,SAAS;4DAAC;4DAAK;4DAAG;yDAAI;oDACxB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,OAAO,IAAI;oDACb;mDAVK;;;;;;;;;;;;;;;;8CAiBb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO,GAAG,SAAS,CAAC,CAAC;4CAAC;4CACjC,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;;;;;;;;;;;sCAOpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAU;;oCAET,WAAW,MAAM;oCACjB,YAAY,MAAM,WAAW,MAAM;oCACnC,YAAY,MAAM,WAAW,MAAM;oCACnC,YAAY,MAAM,WAAW,OAAO;oCACpC,YAAY,OAAO;;+BAVf,KAAK,KAAK,CAAC,WAAW;;;;;;;;;;;;;;;;gBAgBhC,YAAY,qBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBAAC;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA7NgB;KAAA", "debugId": null}}, {"offset": {"line": 4713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Navigation } from '@/components/layout/Navigation';\nimport { Hero } from '@/components/sections/Hero';\nimport { Skills } from '@/components/sections/Skills';\nimport { Projects } from '@/components/sections/Projects';\nimport { Experience } from '@/components/sections/Experience';\nimport { YouTube } from '@/components/sections/YouTube';\nimport { Contact } from '@/components/sections/Contact';\nimport { Footer } from '@/components/layout/Footer';\nimport { LoadingScreen } from '@/components/ui/LoadingScreen';\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n\n  if (isLoading) {\n    return <LoadingScreen onComplete={handleLoadingComplete} />;\n  }\n\n  return (\n    <main className=\"min-h-screen\">\n      <Navigation />\n      <Hero />\n      <Skills />\n      <Projects />\n      <Experience />\n      <YouTube />\n      <Contact />\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,wBAAwB;QAC5B,aAAa;IACf;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC,4IAAA,CAAA,gBAAa;YAAC,YAAY;;;;;;IACpC;IAEA,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,6IAAA,CAAA,aAAU;;;;;0BACX,6LAAC,yIAAA,CAAA,OAAI;;;;;0BACL,6LAAC,2IAAA,CAAA,SAAM;;;;;0BACP,6LAAC,6IAAA,CAAA,WAAQ;;;;;0BACT,6LAAC,+IAAA,CAAA,aAAU;;;;;0BACX,6LAAC,4IAAA,CAAA,UAAO;;;;;0BACR,6LAAC,4IAAA,CAAA,UAAO;;;;;0BACR,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAvBwB;KAAA", "debugId": null}}]}