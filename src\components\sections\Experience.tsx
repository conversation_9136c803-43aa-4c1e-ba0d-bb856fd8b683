'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { Calendar, MapPin, ExternalLink } from 'lucide-react';

const experiences = [
  {
    id: 1,
    title: 'Senior Frontend Developer',
    company: 'TechCorp Solutions',
    location: 'San Francisco, CA',
    period: '2022 - Present',
    description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',
    achievements: [
      'Improved application performance by 40% through code optimization',
      'Led migration from legacy codebase to modern React/TypeScript stack',
      'Mentored 5+ junior developers and established coding standards',
      'Implemented automated testing reducing bugs by 60%'
    ],
    technologies: ['React', 'TypeScript', 'Next.js', 'GraphQL', 'AWS'],
    companyUrl: 'https://techcorp.com',
  },
  {
    id: 2,
    title: 'Full Stack Developer',
    company: 'StartupXYZ',
    location: 'Remote',
    period: '2020 - 2022',
    description: 'Built scalable web applications from concept to deployment, working closely with design and product teams.',
    achievements: [
      'Developed MVP that secured $2M in Series A funding',
      'Built real-time collaboration features using WebSocket',
      'Implemented CI/CD pipeline reducing deployment time by 80%',
      'Designed and developed RESTful APIs serving 10k+ users'
    ],
    technologies: ['React', 'Node.js', 'PostgreSQL', 'Docker', 'Kubernetes'],
    companyUrl: 'https://startupxyz.com',
  },
  {
    id: 3,
    title: 'Frontend Developer',
    company: 'Digital Agency Pro',
    location: 'New York, NY',
    period: '2019 - 2020',
    description: 'Created responsive websites and web applications for diverse clients, focusing on performance and user experience.',
    achievements: [
      'Delivered 20+ client projects with 98% satisfaction rate',
      'Reduced average page load time by 50% across all projects',
      'Implemented accessibility standards achieving WCAG 2.1 AA compliance',
      'Collaborated with UX team to improve conversion rates by 35%'
    ],
    technologies: ['JavaScript', 'React', 'SCSS', 'WordPress', 'Figma'],
    companyUrl: 'https://digitalagencypro.com',
  },
  {
    id: 4,
    title: 'Junior Web Developer',
    company: 'WebDev Studio',
    location: 'Austin, TX',
    period: '2018 - 2019',
    description: 'Started my professional journey building websites and learning modern development practices.',
    achievements: [
      'Completed 15+ website projects using modern frameworks',
      'Learned React, Node.js, and modern development workflows',
      'Contributed to open-source projects and internal tools',
      'Achieved Google PageSpeed scores of 90+ on all projects'
    ],
    technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'Git'],
    companyUrl: 'https://webdevstudio.com',
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, x: -50 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.8,
    },
  },
};

export function Experience() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  return (
    <section id="experience" className="py-20 bg-slate-900/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-display font-bold gradient-text mb-4"
          >
            Professional Experience
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-300 max-w-3xl mx-auto"
          >
            My journey through the tech industry, building innovative solutions 
            and growing as a developer
          </motion.p>
        </motion.div>

        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-teal-500 via-cyan-400 to-teal-500"></div>

          <motion.div
            variants={containerVariants}
            className="space-y-12"
          >
            {experiences.map((experience, index) => (
              <motion.div
                key={experience.id}
                variants={itemVariants}
                className={`relative flex items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                } flex-col md:space-x-8`}
              >
                {/* Timeline Dot */}
                <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-teal-400 rounded-full border-4 border-navy z-10 shadow-lg shadow-teal-400/50"></div>

                {/* Content Card */}
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  className={`w-full md:w-5/12 ml-16 md:ml-0 ${
                    index % 2 === 0 ? 'md:mr-auto' : 'md:ml-auto'
                  }`}
                >
                  <div className="glass rounded-2xl p-6 hover:glass-strong transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-white mb-1">
                          {experience.title}
                        </h3>
                        <div className="flex items-center space-x-2 mb-2">
                          <motion.a
                            href={experience.companyUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-teal-400 font-medium hover:text-teal-300 transition-colors duration-200 flex items-center space-x-1"
                            whileHover={{ scale: 1.05 }}
                          >
                            <span>{experience.company}</span>
                            <ExternalLink className="w-3 h-3" />
                          </motion.a>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-slate-400">
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-4 h-4" />
                            <span>{experience.period}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="w-4 h-4" />
                            <span>{experience.location}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <p className="text-slate-300 mb-4 leading-relaxed">
                      {experience.description}
                    </p>

                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-white mb-2">Key Achievements:</h4>
                      <ul className="space-y-1">
                        {experience.achievements.map((achievement, achievementIndex) => (
                          <motion.li
                            key={achievementIndex}
                            initial={{ opacity: 0, x: -10 }}
                            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}
                            transition={{ duration: 0.4, delay: index * 0.3 + achievementIndex * 0.1 }}
                            className="text-sm text-slate-300 flex items-start space-x-2"
                          >
                            <span className="text-teal-400 mt-1">•</span>
                            <span>{achievement}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-sm font-semibold text-white mb-2">Technologies:</h4>
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.map((tech, techIndex) => (
                          <motion.span
                            key={tech}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, delay: index * 0.3 + techIndex * 0.05 }}
                            className="px-2 py-1 text-xs font-medium bg-teal-500/20 text-teal-400 rounded"
                          >
                            {tech}
                          </motion.span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="text-center mt-16"
        >
          <div className="glass rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Ready to Work Together?
            </h3>
            <p className="text-slate-300 mb-6">
              I'm always interested in new opportunities and exciting projects. 
              Let's discuss how we can bring your ideas to life.
            </p>
            <motion.a
              href="#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-teal-500 to-cyan-400 text-navy font-semibold rounded-lg hover:from-teal-400 hover:to-cyan-300 transition-all duration-300 neon-glow"
            >
              Get In Touch
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
