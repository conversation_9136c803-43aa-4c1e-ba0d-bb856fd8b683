import { NextRequest, NextResponse } from 'next/server';

// YouTube Data API v3 integration
// To use this, you'll need to:
// 1. Get a YouTube Data API key from Google Cloud Console
// 2. Add YOUTUBE_API_KEY to your environment variables
// 3. Replace CHANNEL_ID with your actual YouTube channel ID

const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
const CHANNEL_ID = process.env.YOUTUBE_CHANNEL_ID || 'YOUR_CHANNEL_ID';
const MAX_RESULTS = 6;

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  viewCount?: string;
  duration?: string;
  url: string;
}

export async function GET(request: NextRequest) {
  try {
    if (!YOUTUBE_API_KEY) {
      return NextResponse.json(
        { error: 'YouTube API key not configured' },
        { status: 500 }
      );
    }

    // Fetch latest videos from the channel
    const searchUrl = `https://www.googleapis.com/youtube/v3/search?key=${YOUTUBE_API_KEY}&channelId=${CHANNEL_ID}&part=snippet&order=date&maxResults=${MAX_RESULTS}&type=video`;
    
    const searchResponse = await fetch(searchUrl);
    
    if (!searchResponse.ok) {
      throw new Error(`YouTube API error: ${searchResponse.status}`);
    }
    
    const searchData = await searchResponse.json();
    
    if (!searchData.items || searchData.items.length === 0) {
      return NextResponse.json({ videos: [] });
    }

    // Get video IDs for additional details
    const videoIds = searchData.items.map((item: any) => item.id.videoId).join(',');
    
    // Fetch video statistics and content details
    const videosUrl = `https://www.googleapis.com/youtube/v3/videos?key=${YOUTUBE_API_KEY}&id=${videoIds}&part=statistics,contentDetails`;
    
    const videosResponse = await fetch(videosUrl);
    const videosData = await videosResponse.json();
    
    // Combine search results with video details
    const videos: YouTubeVideo[] = searchData.items.map((item: any) => {
      const videoDetails = videosData.items?.find((video: any) => video.id === item.id.videoId);
      
      return {
        id: item.id.videoId,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnail: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default.url,
        publishedAt: item.snippet.publishedAt,
        viewCount: videoDetails?.statistics?.viewCount ? formatViewCount(videoDetails.statistics.viewCount) : undefined,
        duration: videoDetails?.contentDetails?.duration ? formatDuration(videoDetails.contentDetails.duration) : undefined,
        url: `https://www.youtube.com/watch?v=${item.id.videoId}`,
      };
    });

    return NextResponse.json({ videos });
  } catch (error) {
    console.error('YouTube API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch YouTube videos' },
      { status: 500 }
    );
  }
}

// Helper function to format view count
function formatViewCount(count: string): string {
  const num = parseInt(count);
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// Helper function to format ISO 8601 duration to readable format
function formatDuration(duration: string): string {
  const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
  if (!match) return '';
  
  const hours = match[1] ? parseInt(match[1].slice(0, -1)) : 0;
  const minutes = match[2] ? parseInt(match[2].slice(0, -1)) : 0;
  const seconds = match[3] ? parseInt(match[3].slice(0, -1)) : 0;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

// Example environment variables to add to your .env.local:
// YOUTUBE_API_KEY=your_youtube_api_key_here
// YOUTUBE_CHANNEL_ID=your_channel_id_here
