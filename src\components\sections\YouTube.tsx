'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { Play, ExternalLink, Calendar, Eye } from 'lucide-react';

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  viewCount?: string;
  duration?: string;
  url: string;
}

// Mock data for demonstration - replace with actual API call
const mockVideos: YouTubeVideo[] = [
  {
    id: '1',
    title: 'Building a Modern React Portfolio with Next.js and Framer Motion',
    description: 'Learn how to create a stunning portfolio website using the latest web technologies including Next.js, TypeScript, and Framer Motion animations.',
    thumbnail: '/api/placeholder/480/360',
    publishedAt: '2024-01-15',
    viewCount: '12,543',
    duration: '24:15',
    url: 'https://youtube.com/watch?v=example1',
  },
  {
    id: '2',
    title: 'Advanced TypeScript Patterns for React Developers',
    description: 'Dive deep into advanced TypeScript patterns that will make your React code more robust, maintainable, and type-safe.',
    thumbnail: '/api/placeholder/480/360',
    publishedAt: '2024-01-08',
    viewCount: '8,921',
    duration: '18:42',
    url: 'https://youtube.com/watch?v=example2',
  },
  {
    id: '3',
    title: 'CSS Grid vs Flexbox: When to Use Which?',
    description: 'A comprehensive comparison of CSS Grid and Flexbox with practical examples and real-world use cases.',
    thumbnail: '/api/placeholder/480/360',
    publishedAt: '2024-01-01',
    viewCount: '15,672',
    duration: '16:28',
    url: 'https://youtube.com/watch?v=example3',
  },
  {
    id: '4',
    title: 'Building Responsive Layouts with Tailwind CSS',
    description: 'Master responsive design with Tailwind CSS utility classes and create beautiful, mobile-first layouts.',
    thumbnail: '/api/placeholder/480/360',
    publishedAt: '2023-12-25',
    viewCount: '9,834',
    duration: '21:33',
    url: 'https://youtube.com/watch?v=example4',
  },
  {
    id: '5',
    title: 'State Management in React: Redux vs Zustand vs Context',
    description: 'Compare different state management solutions for React applications and learn when to use each approach.',
    thumbnail: '/api/placeholder/480/360',
    publishedAt: '2023-12-18',
    viewCount: '11,205',
    duration: '28:17',
    url: 'https://youtube.com/watch?v=example5',
  },
  {
    id: '6',
    title: 'Optimizing React Performance: Tips and Tricks',
    description: 'Learn essential techniques to optimize your React applications for better performance and user experience.',
    thumbnail: '/api/placeholder/480/360',
    publishedAt: '2023-12-11',
    viewCount: '7,456',
    duration: '19:52',
    url: 'https://youtube.com/watch?v=example6',
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function YouTube() {
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  useEffect(() => {
    // Simulate API call - replace with actual YouTube API integration
    const fetchVideos = async () => {
      try {
        setLoading(true);
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        setVideos(mockVideos);
      } catch (err) {
        setError('Failed to load videos');
      } finally {
        setLoading(false);
      }
    };

    if (isInView) {
      fetchVideos();
    }
  }, [isInView]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatViewCount = (count: string) => {
    return count;
  };

  return (
    <section id="youtube" className="py-20 bg-navy/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-display font-bold gradient-text mb-4"
          >
            Latest YouTube Videos
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-300 max-w-3xl mx-auto"
          >
            Sharing knowledge through tutorials, tips, and insights about 
            modern web development and design
          </motion.p>
        </motion.div>

        {loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="glass rounded-2xl overflow-hidden animate-pulse">
                <div className="aspect-video bg-slate-700"></div>
                <div className="p-6 space-y-3">
                  <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-700 rounded w-full"></div>
                  <div className="h-3 bg-slate-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="glass rounded-2xl p-8 max-w-md mx-auto">
              <p className="text-red-400 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors duration-200"
              >
                Try Again
              </button>
            </div>
          </motion.div>
        )}

        {!loading && !error && (
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {videos.map((video, index) => (
              <motion.div
                key={video.id}
                variants={itemVariants}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group glass rounded-2xl overflow-hidden hover:glass-strong transition-all duration-500"
              >
                <div className="relative aspect-video overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-teal-500/20 to-cyan-500/20 flex items-center justify-center">
                    <Play className="w-16 h-16 text-teal-400/50" />
                  </div>
                  
                  {/* Play Button Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center cursor-pointer"
                    >
                      <Play className="w-6 h-6 text-white ml-1" fill="currentColor" />
                    </motion.div>
                  </div>

                  {/* Duration Badge */}
                  {video.duration && (
                    <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/80 text-white text-xs rounded">
                      {video.duration}
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2 group-hover:text-teal-400 transition-colors duration-300">
                    {video.title}
                  </h3>
                  
                  <p className="text-slate-300 text-sm mb-4 line-clamp-3 leading-relaxed">
                    {video.description}
                  </p>

                  <div className="flex items-center justify-between text-xs text-slate-400 mb-4">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(video.publishedAt)}</span>
                    </div>
                    {video.viewCount && (
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{formatViewCount(video.viewCount)} views</span>
                      </div>
                    )}
                  </div>

                  <motion.a
                    href={video.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                  >
                    <Play className="w-4 h-4" />
                    <span>Watch Video</span>
                    <ExternalLink className="w-3 h-3" />
                  </motion.a>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Channel CTA */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="text-center mt-16"
        >
          <div className="glass rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Subscribe for More Content
            </h3>
            <p className="text-slate-300 mb-6">
              Join thousands of developers learning modern web development. 
              New tutorials every week covering React, TypeScript, and more!
            </p>
            <motion.a
              href="https://youtube.com/@yourchannel"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center space-x-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-colors duration-300"
            >
              <Play className="w-5 h-5" />
              <span>Subscribe on YouTube</span>
              <ExternalLink className="w-4 h-4" />
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
