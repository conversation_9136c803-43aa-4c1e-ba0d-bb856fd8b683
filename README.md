# 🚀 Futuristic Portfolio Website

A modern, elegant, and fully responsive personal portfolio website built with Next.js, TypeScript, Tailwind CSS, and Framer Motion. Features a sleek Apple-like design with glassmorphism effects, smooth animations, and a dark/light theme toggle.

## ✨ Features

- **Modern Design**: Minimal, sleek interface with Apple-inspired aesthetics
- **Glassmorphism Effects**: Beautiful glass-like components with backdrop blur
- **Smooth Animations**: Framer Motion powered animations and micro-interactions
- **Responsive Design**: Mobile-first approach, works on all devices
- **Dark/Light Mode**: Seamless theme switching with smooth transitions
- **YouTube Integration**: Dynamically fetch and display latest videos
- **Contact Form**: Functional contact form with validation
- **SEO Optimized**: Meta tags, Open Graph, and Twitter Card support
- **Performance Focused**: Optimized for Core Web Vitals
- **Accessibility**: WCAG 2.1 AA compliant

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Sora & Inter (Google Fonts)
- **Theme**: next-themes
- **Deployment**: Vercel (recommended)

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables** (optional)
   ```bash
   cp .env.example .env.local
   ```

   Add your YouTube API credentials:
   ```env
   YOUTUBE_API_KEY=your_youtube_api_key_here
   YOUTUBE_CHANNEL_ID=your_channel_id_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── sections/         # Page sections
│   └── ui/               # Reusable UI components
└── lib/                  # Utility functions
```

## 🎨 Customization

### Personal Information

Update the following files with your information:

1. **Hero Section** (`src/components/sections/Hero.tsx`)
   - Name, title, description
   - Social media links
   - Profile image

2. **Skills** (`src/components/sections/Skills.tsx`)
   - Add/remove skills and technologies
   - Update skill levels

3. **Projects** (`src/components/sections/Projects.tsx`)
   - Add your projects
   - Update project details, images, and links

4. **Experience** (`src/components/sections/Experience.tsx`)
   - Add your work experience
   - Update job details and achievements

5. **Contact** (`src/components/sections/Contact.tsx`)
   - Update contact information
   - Configure form submission

### Styling

The design system is built with CSS custom properties and Tailwind CSS:

- **Colors**: Defined in `src/app/globals.css`
- **Fonts**: Configured in `src/app/layout.tsx`
- **Components**: Styled with Tailwind classes and custom CSS

### YouTube Integration

To enable YouTube video fetching:

1. Get a YouTube Data API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Find your YouTube channel ID
3. Add them to your `.env.local` file
4. The API route at `/api/youtube` will handle video fetching

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add environment variables in Vercel dashboard
4. Deploy!

### Other Platforms

The app can be deployed to any platform that supports Next.js:

- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS
- [Framer Motion](https://www.framer.com/motion/) for smooth animations
- [Lucide](https://lucide.dev/) for beautiful icons

---

**Built with ❤️ and lots of ☕**
