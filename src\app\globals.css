@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --navy: #0f172a;
  --teal: #14b8a6;
  --neon: #00ffff;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-navy: var(--navy);
  --color-teal: var(--teal);
  --color-neon: var(--neon);
  --color-glass-bg: var(--glass-bg);
  --color-glass-border: var(--glass-border);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Sora', system-ui, sans-serif;
  --animation-duration-fast: 0.2s;
  --animation-duration-normal: 0.3s;
  --animation-duration-slow: 0.5s;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
    --glass-bg: rgba(15, 23, 42, 0.8);
    --glass-border: rgba(148, 163, 184, 0.1);
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-teal);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-neon);
}

/* Glassmorphism utility classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--color-teal), var(--color-neon));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Neon glow effect */
.neon-glow {
  box-shadow: 0 0 20px rgba(20, 184, 166, 0.3);
}

.neon-glow:hover {
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}
