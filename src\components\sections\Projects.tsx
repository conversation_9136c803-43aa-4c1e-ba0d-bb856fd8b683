'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { ExternalLink, Github, Eye } from 'lucide-react';
import { Button } from '@/components/ui/Button';

const projects = [
  {
    id: 1,
    title: 'E-Commerce Platform',
    description: 'A modern, full-stack e-commerce solution with real-time inventory management, secure payments, and an intuitive admin dashboard.',
    image: '/api/placeholder/600/400',
    tags: ['Next.js', 'TypeScript', 'Stripe', 'PostgreSQL', 'Tailwind CSS'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/example',
    featured: true,
  },
  {
    id: 2,
    title: 'AI-Powered Analytics Dashboard',
    description: 'Interactive dashboard with machine learning insights, real-time data visualization, and predictive analytics for business intelligence.',
    image: '/api/placeholder/600/400',
    tags: ['React', 'Python', 'TensorFlow', 'D3.js', 'FastAPI'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/example',
    featured: true,
  },
  {
    id: 3,
    title: 'Social Media App',
    description: 'A responsive social platform with real-time messaging, content sharing, and advanced privacy controls.',
    image: '/api/placeholder/600/400',
    tags: ['React Native', 'Node.js', 'Socket.io', 'MongoDB', 'AWS'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/example',
    featured: false,
  },
  {
    id: 4,
    title: 'Design System Library',
    description: 'Comprehensive component library with documentation, accessibility features, and theme customization.',
    image: '/api/placeholder/600/400',
    tags: ['React', 'Storybook', 'TypeScript', 'CSS-in-JS', 'Jest'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/example',
    featured: false,
  },
  {
    id: 5,
    title: 'Fitness Tracking App',
    description: 'Mobile-first fitness application with workout planning, progress tracking, and social features.',
    image: '/api/placeholder/600/400',
    tags: ['React Native', 'Firebase', 'Redux', 'Chart.js', 'Expo'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/example',
    featured: false,
  },
  {
    id: 6,
    title: 'Portfolio Website',
    description: 'This very portfolio! A showcase of modern web development with smooth animations and responsive design.',
    image: '/api/placeholder/600/400',
    tags: ['Next.js', 'TypeScript', 'Framer Motion', 'Tailwind CSS'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com/example',
    featured: false,
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function Projects() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  const featuredProjects = projects.filter(project => project.featured);
  const otherProjects = projects.filter(project => !project.featured);

  return (
    <section id="projects" className="py-20 bg-navy/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-display font-bold gradient-text mb-4"
          >
            Featured Projects
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-300 max-w-3xl mx-auto"
          >
            A collection of projects that showcase my skills in modern web development, 
            UI/UX design, and problem-solving
          </motion.p>
        </motion.div>

        {/* Featured Projects */}
        <motion.div
          variants={containerVariants}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16"
        >
          {featuredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              variants={itemVariants}
              whileHover={{ y: -10 }}
              className="group glass rounded-2xl overflow-hidden hover:glass-strong transition-all duration-500"
            >
              <div className="relative overflow-hidden">
                <div className="aspect-video bg-gradient-to-br from-teal-500/20 to-cyan-500/20 flex items-center justify-center">
                  <Eye className="w-16 h-16 text-teal-400/50" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-navy/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <motion.a
                    href={project.liveUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 glass rounded-full hover:glass-strong"
                  >
                    <ExternalLink className="w-4 h-4 text-white" />
                  </motion.a>
                  <motion.a
                    href={project.githubUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 glass rounded-full hover:glass-strong"
                  >
                    <Github className="w-4 h-4 text-white" />
                  </motion.a>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-teal-400 transition-colors duration-300">
                  {project.title}
                </h3>
                <p className="text-slate-300 mb-4 leading-relaxed">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 text-xs font-medium bg-teal-500/20 text-teal-400 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                
                <div className="flex space-x-3">
                  <Button variant="secondary" size="sm" className="flex-1">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Live Demo
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Github className="w-4 h-4 mr-2" />
                    Code
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Other Projects Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h3 className="text-2xl font-semibold text-center text-white mb-8">
            More Projects
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group glass rounded-xl p-6 hover:glass-strong transition-all duration-300"
              >
                <div className="flex items-start justify-between mb-4">
                  <h4 className="text-lg font-semibold text-white group-hover:text-teal-400 transition-colors duration-300">
                    {project.title}
                  </h4>
                  <div className="flex space-x-2">
                    <motion.a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.1 }}
                      className="text-slate-400 hover:text-teal-400 transition-colors duration-200"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </motion.a>
                    <motion.a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.1 }}
                      className="text-slate-400 hover:text-teal-400 transition-colors duration-200"
                    >
                      <Github className="w-4 h-4" />
                    </motion.a>
                  </div>
                </div>
                
                <p className="text-slate-300 text-sm mb-4 leading-relaxed">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-1">
                  {project.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs font-medium bg-slate-700/50 text-slate-400 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                  {project.tags.length > 3 && (
                    <span className="px-2 py-1 text-xs font-medium text-slate-500">
                      +{project.tags.length - 3} more
                    </span>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
